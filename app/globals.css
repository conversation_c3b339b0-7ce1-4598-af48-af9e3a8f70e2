@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  /* Modern color palette */
  --primary-50: #f0fdf4;
  --primary-100: #dcfce7;
  --primary-200: #bbf7d0;
  --primary-300: #86efac;
  --primary-400: #4ade80;
  --primary-500: #22c55e;
  --primary-600: #16a34a;
  --primary-700: #15803d;
  --primary-800: #166534;
  --primary-900: #14532d;
  
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  
  --accent-50: #f8fafc;
  --accent-100: #f1f5f9;
  --accent-200: #e2e8f0;
  --accent-300: #cbd5e1;
  --accent-400: #94a3b8;
  --accent-500: #64748b;
  --accent-600: #475569;
  --accent-700: #334155;
  --accent-800: #1e293b;
  --accent-900: #0f172a;
  
  /* Modern shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--neutral-800);
  background-color: var(--neutral-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern typography scale */
.text-display {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-heading-1 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.text-heading-2 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.text-heading-3 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 600;
  line-height: 1.4;
}

.text-body-lg {
  font-size: 1.125rem;
  line-height: 1.75;
  font-weight: 400;
}

.text-body {
  font-size: 1rem;
  line-height: 1.75;
  font-weight: 400;
}

.text-body-sm {
  font-size: 0.875rem;
  line-height: 1.6;
  font-weight: 400;
}

.text-caption {
  font-size: 0.75rem;
  line-height: 1.5;
  font-weight: 500;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

/* Modern gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-50) 100%);
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--accent-50) 0%, white 100%);
}

.bg-gradient-subtle {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.03) 0%, rgba(34, 197, 94, 0.08) 100%);
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern grid patterns */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, var(--neutral-200) 1px, transparent 1px),
    linear-gradient(to bottom, var(--neutral-200) 1px, transparent 1px);
  background-size: 32px 32px;
  opacity: 0.5;
}

.bg-grid-dots {
  background-image: radial-gradient(circle, var(--neutral-300) 1px, transparent 1px);
  background-size: 16px 16px;
  opacity: 0.3;
}

/* Modern button styles */
.btn-primary {
  @apply inline-flex items-center justify-center rounded-xl bg-gradient-to-r from-[var(--primary-600)] to-[var(--primary-700)] px-6 py-3 text-sm font-semibold text-white shadow-lg shadow-[var(--primary-600)]/25 transition-all duration-300 hover:shadow-xl hover:shadow-[var(--primary-600)]/30 hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--primary-600)] active:scale-95;
}

.btn-secondary {
  @apply inline-flex items-center justify-center rounded-xl bg-white px-6 py-3 text-sm font-semibold text-[var(--primary-700)] shadow-md shadow-[var(--neutral-200)]/50 ring-1 ring-[var(--primary-200)] transition-all duration-300 hover:shadow-lg hover:ring-[var(--primary-300)] hover:scale-105 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--primary-600)] active:scale-95;
}

.btn-ghost {
  @apply inline-flex items-center justify-center rounded-xl px-4 py-2 text-sm font-medium text-[var(--neutral-600)] transition-all duration-300 hover:bg-[var(--neutral-100)] hover:text-[var(--neutral-800)] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--primary-600)] active:scale-95;
}

.btn-outline {
  @apply inline-flex items-center justify-center rounded-xl border border-[var(--neutral-300)] bg-white px-6 py-3 text-sm font-semibold text-[var(--neutral-700)] shadow-sm transition-all duration-300 hover:border-[var(--neutral-400)] hover:bg-[var(--neutral-50)] hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--primary-600)] active:scale-95;
}

/* Modern card styles */
.card {
  @apply rounded-2xl bg-white shadow-sm transition-all duration-300 hover:shadow-lg;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1 hover:scale-[1.02];
}

.card-glass {
  @apply rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg transition-all duration-300 hover:shadow-xl hover:bg-white/90;
}

/* Animation classes */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out forwards;
}

.animate-slide-down {
  animation: slide-down 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Staggered animations */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Modern focus styles */
.focus-ring {
  @apply focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[var(--primary-600)];
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: 3px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* Selection styles */
::selection {
  background: var(--primary-200);
  color: var(--primary-900);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    @apply w-full;
  }
}

/* Modern gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-[var(--primary-600)] to-[var(--primary-700)] bg-clip-text text-transparent;
}

.gradient-text-secondary {
  @apply bg-gradient-to-r from-[var(--neutral-600)] to-[var(--neutral-800)] bg-clip-text text-transparent;
}

/* Modern section spacing */
.section {
  @apply py-20 sm:py-24 lg:py-32;
}

.section-sm {
  @apply py-16 sm:py-20;
}

.section-lg {
  @apply py-24 sm:py-32 lg:py-40;
}

/* Container utilities */
.container-sm {
  @apply mx-auto max-w-3xl px-4 sm:px-6 lg:px-8;
}

.container-md {
  @apply mx-auto max-w-5xl px-4 sm:px-6 lg:px-8;
}

.container-lg {
  @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
}

/* Modern loading states */
.loading-shimmer {
  @apply relative overflow-hidden bg-[var(--neutral-200)];
}

.loading-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Modern hover states */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Modern border styles */
.border-subtle {
  @apply border-[var(--neutral-200)];
}

.border-strong {
  @apply border-[var(--neutral-300)];
}

/* Modern background utilities */
.bg-subtle {
  @apply bg-[var(--neutral-50)];
}

.bg-surface {
  @apply bg-white;
}

.bg-surface-elevated {
  @apply bg-white shadow-sm;
}