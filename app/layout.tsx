import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "LibraryCloud - Modern Library Management System",
  description: "Transform your library with our cloud-based management system. Streamline cataloging, automate circulation, and unlock powerful analytics.",
  keywords: "library management system, library software, cataloging, circulation, analytics, digital library",
  authors: [{ name: "LibraryCloud" }],
  openGraph: {
    title: "LibraryCloud - Modern Library Management System",
    description: "Transform your library with our cloud-based management system",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "LibraryCloud - Modern Library Management System",
    description: "Transform your library with our cloud-based management system",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} font-sans antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
