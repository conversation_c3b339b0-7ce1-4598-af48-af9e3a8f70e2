"use client";

import Image from "next/image";
import Link from "next/link";
import Header from "./components/Header";
import { useEffect } from "react";

export default function Home() {
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute("href")?.startsWith("#")) {
        e.preventDefault();
        const href = target.getAttribute("href");
        if (href) {
          const element = document.querySelector(href);
          if (element) {
            element.scrollIntoView({ behavior: "smooth" });
          }
        }
      }
    };

    document.addEventListener("click", handleAnchorClick);
    return () => document.removeEventListener("click", handleAnchorClick);
  }, []);

  const features = [
    {
      name: "Smart Cataloging",
      description:
        "Auto-catalog with ISBN lookup, bulk imports, and AI-powered metadata enrichment. Support for all formats including digital resources and multimedia.",
      icon: "📚",
      stats: "1M+ books cataloged",
    },
    {
      name: "Effortless Circulation",
      description:
        "Self-checkout kiosks, mobile apps, and automated renewals. RFID integration and real-time availability across all library locations.",
      icon: "🔄",
      stats: "99.9% uptime",
    },
    {
      name: "Powerful Analytics",
      description:
        "Track usage patterns, popular titles, and patron engagement. Generate comprehensive reports for stakeholders and funding applications.",
      icon: "📊",
      stats: "500+ reports",
    },
    {
      name: "Digital Collections",
      description:
        "Manage e-books, audiobooks, and digital resources with seamless integration. Support for multiple formats and DRM management.",
      icon: "💾",
      stats: "50K+ digital assets",
    },
    {
      name: "Patron Management",
      description:
        "Complete patron lifecycle management from registration to engagement tracking. Customizable membership tiers and fine management.",
      icon: "👥",
      stats: "100K+ active patrons",
    },
    {
      name: "Mobile Experience",
      description:
        "Native mobile apps for iOS and Android with offline capabilities. Patrons can browse, reserve, and manage their accounts on the go.",
      icon: "📱",
      stats: "4.8★ app rating",
    },
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Head Librarian, Boston Public",
      image:
        "https://images.unsplash.com/photo-*************-2616b612b786?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      content:
        "Our circulation increased 47% in the first quarter. The self-checkout feature alone saved us 15 hours per week.",
      rating: 5,
    },
    {
      name: "Michael Chen",
      role: "Director, Seattle University",
      image:
        "https://images.unsplash.com/photo-*************-f4e0f30006d5?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      content:
        "Implementation took just 2 weeks. Our students love the mobile app, and our staff finally has time for community programs.",
      rating: 5,
    },
    {
      name: "David Rodriguez",
      role: "Library Manager, Austin ISD",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
      content:
        "The analytics dashboard helped us secure $50K in additional funding by showing real impact data to our school board.",
      rating: 5,
    },
  ];

  const stats = [
    { value: "2,000+", label: "Libraries Worldwide" },
    { value: "50M+", label: "Books Managed" },
    { value: "99.9%", label: "Uptime" },
    { value: "4.8★", label: "User Rating" },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />

      {/* Hero Section - Modern Design */}
      <section className="relative overflow-hidden bg-gradient-subtle pt-20">
        <div className="absolute inset-0 bg-grid-dots"></div>
        <div className="relative container-lg py-24 sm:py-32">
          <div className="grid lg:grid-cols-2 gap-16 lg:gap-20 items-center">
            <div className="text-center lg:text-left animate-slide-up">
              <div className="inline-flex items-center rounded-full bg-primary-100 px-4 py-1.5 text-sm font-medium text-primary-700 mb-6">
                🚀 Trusted by 2,000+ libraries worldwide
              </div>
              <h1 className="text-display text-neutral-900">
                Transform Your Library Into a{" "}
                <span className="gradient-text">Digital Powerhouse</span>
              </h1>
              <p className="text-body-lg text-neutral-600 mt-6 max-w-2xl mx-auto lg:mx-0">
                Join libraries worldwide using our cloud-based system to
                streamline cataloging, automate circulation, and unlock powerful
                analytics—all in one intuitive platform.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link href="/get-started" className="btn-primary">
                  Start Free Trial
                  <span className="ml-2">→</span>
                </Link>
                <Link href="#demo" className="btn-secondary">
                  Watch Demo
                </Link>
              </div>
              <div className="mt-8 flex items-center justify-center lg:justify-start gap-8 text-sm text-neutral-500">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>30-day free trial</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>No credit card required</span>
                </div>
              </div>
            </div>
            <div className="relative animate-scale-in stagger-1">
              <div className="relative mx-auto w-full max-w-lg">
                <div className="absolute -inset-8 bg-gradient-to-r from-primary-600/20 to-primary-700/20 rounded-3xl blur-3xl"></div>
                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="h-3 bg-neutral-200 rounded-full w-3/4"></div>
                      <div className="h-3 w-3 bg-primary-500 rounded-full"></div>
                    </div>
                    <div className="space-y-4">
                      <div className="h-4 bg-neutral-100 rounded-lg"></div>
                      <div className="h-4 bg-neutral-100 rounded-lg w-5/6"></div>
                      <div className="h-4 bg-neutral-100 rounded-lg w-4/5"></div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mt-8">
                      <div className="h-24 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl"></div>
                      <div className="h-24 bg-gradient-to-br from-primary-200 to-primary-300 rounded-2xl"></div>
                      <div className="h-24 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section - Modern Cards */}
      <section className="section-sm bg-white">
        <div className="container-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center animate-slide-up stagger-1"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl mb-4">
                  <span className="text-2xl font-bold text-primary-700">
                    {stat.value.split("")[0]}
                  </span>
                </div>
                <div className="text-3xl md:text-4xl font-bold text-neutral-900">
                  {stat.value}
                </div>
                <div className="text-sm text-neutral-600 mt-2 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section - Modern Grid */}
      <section id="features" className="section bg-gradient-accent">
        <div className="container-lg">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-flex items-center rounded-full bg-primary-100 px-4 py-1.5 text-sm font-medium text-primary-700 mb-4">
              Everything You Need
            </div>
            <h2 className="text-heading-1 text-neutral-900">
              Built for Modern Libraries
            </h2>
            <p className="text-body-lg text-neutral-600 mt-6">
              Our comprehensive platform covers every aspect of library
              management, from cataloging to circulation, analytics to digital
              collections.
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {features.map((feature, index) => (
              <div
                key={feature.name}
                className="card card-hover p-8 group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-3xl">{feature.icon}</span>
                </div>
                <h3 className="text-heading-3 text-neutral-900 mb-3">
                  {feature.name}
                </h3>
                <p className="text-body text-neutral-600 mb-4">
                  {feature.description}
                </p>
                <div className="inline-flex items-center text-sm font-semibold text-primary-700">
                  {feature.stats}
                  <span className="ml-2">→</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section - Modern Layout */}
      <section id="about" className="section bg-white">
        <div className="container-lg">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-slide-up">
              <div className="inline-flex items-center rounded-full bg-primary-100 px-4 py-1.5 text-sm font-medium text-primary-700 mb-4">
                Why Choose LibraryCloud?
              </div>
              <h2 className="text-heading-1 text-neutral-900 mb-6">
                More Than Just Library Management
              </h2>
              <p className="text-body-lg text-neutral-600 mb-8">
                LibraryCloud is a complete digital transformation platform
                designed specifically for modern libraries. We understand the
                unique challenges libraries face today and provide solutions
                that grow with your needs.
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "Cloud-Based & Scalable",
                    description:
                      "Access from anywhere, scale as you grow with enterprise-grade infrastructure.",
                  },
                  {
                    title: "24/7 Support & Training",
                    description:
                      "Expert support team and comprehensive training programs for smooth adoption.",
                  },
                  {
                    title: "Security & Compliance",
                    description:
                      "Enterprise-grade security with GDPR and CCPA compliance built-in.",
                  },
                ].map((item, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center">
                      <span className="text-primary-700 font-bold text-sm">
                        ✓
                      </span>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-neutral-900 mb-1">
                        {item.title}
                      </h4>
                      <p className="text-body text-neutral-600">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative animate-scale-in stagger-2">
              <div className="relative mx-auto w-full max-w-lg">
                <div className="absolute -inset-4 bg-gradient-to-r from-primary-600/10 to-primary-700/10 rounded-3xl blur-2xl"></div>
                <div className="relative bg-white rounded-3xl shadow-xl p-8 border border-neutral-200">
                  <div className="space-y-6">
                    {[
                      {
                        icon: "📈",
                        title: "Real-time Analytics",
                        description: "Track everything in real-time",
                      },
                      {
                        icon: "🔒",
                        title: "Bank-level Security",
                        description: "Your data is always protected",
                      },
                      {
                        icon: "🚀",
                        title: "Lightning Fast",
                        description: "Optimized for speed and performance",
                      },
                    ].map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-4 p-4 rounded-2xl bg-neutral-50 hover:bg-neutral-100 transition-colors duration-300"
                      >
                        <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center">
                          <span className="text-2xl">{item.icon}</span>
                        </div>
                        <div>
                          <div className="font-semibold text-neutral-900">
                            {item.title}
                          </div>
                          <div className="text-sm text-neutral-600">
                            {item.description}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Modern Cards */}
      <section className="section bg-gradient-accent">
        <div className="container-lg">
          <div className="text-center max-w-3xl mx-auto">
            <div className="inline-flex items-center rounded-full bg-primary-100 px-4 py-1.5 text-sm font-medium text-primary-700 mb-4">
              Loved by librarians worldwide
            </div>
            <h2 className="text-heading-1 text-neutral-900">
              What Librarians Are Saying
            </h2>
            <p className="text-body-lg text-neutral-600 mt-6">
              Don't just take our word for it. Here's what librarians are saying
              about LibraryCloud.
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.name}
                className="card card-hover p-8"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center mb-6">
                  <Image
                    className="h-14 w-14 rounded-full ring-4 ring-white shadow-md"
                    src={testimonial.image}
                    alt={testimonial.name}
                    width={56}
                    height={56}
                  />
                  <div className="ml-4">
                    <div className="font-semibold text-neutral-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-neutral-600">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
                <blockquote className="text-body text-neutral-700 mb-6">
                  <p>"{testimonial.content}"</p>
                </blockquote>
                <div className="flex gap-x-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg
                      key={i}
                      className="h-5 w-5 text-yellow-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.868 2.884c.321-.772 1.415-.772 1.736 0l1.681 4.06c.064.155.196.281.354.331l4.48 1.087c.803.195 1.13 1.116.532 1.684l-3.388 3.014a.998.998 0 00-.285.902l.802 4.402c.123.676-.557 1.204-1.12.93L10 15.482l-3.904 2.082c-.563.274-1.243-.254-1.12-.93l.802-4.402a.998.998 0 00-.285-.902l-3.388-3.014c-.598-.568-.27-1.489.532-1.684l4.48-1.087a.998.998 0 00.354-.331l1.681-4.06z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section - Modern CTA */}
      <section
        id="pricing"
        className="section bg-gradient-to-br from-primary-600 to-primary-700"
      >
        <div className="container-lg text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-heading-1 text-white">
              Simple, transparent pricing
            </h2>
            <p className="text-body-lg text-primary-100 mt-6">
              Start free, scale as you grow. No hidden fees, no long-term
              contracts. Perfect for libraries of any size.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/pricing"
                className="inline-flex items-center justify-center rounded-xl bg-white text-primary-700 px-8 py-4 text-lg font-semibold shadow-lg shadow-primary-900/20 transition-all duration-300 hover:shadow-xl hover:scale-105"
              >
                View Full Pricing
                <span className="ml-2">→</span>
              </Link>
            </div>
            <div className="mt-6">
              <div className="text-primary-100">
                Starting at{" "}
                <span className="text-2xl font-bold text-white">$99/month</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section - Modern Design */}
      <section className="section bg-white">
        <div className="container-lg">
          <div className="relative isolate overflow-hidden bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 px-8 py-20 text-center rounded-3xl sm:px-16">
            <div className="absolute inset-0 bg-grid-dots opacity-10"></div>
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-600/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-700/20 rounded-full blur-3xl"></div>

            <div className="relative">
              <h2 className="text-heading-1 text-white">
                Ready to Transform Your Library?
              </h2>
              <p className="text-body-lg text-neutral-300 mt-6 max-w-2xl mx-auto">
                Join the 2,000+ libraries already using our platform. Get
                started today with our 30-day free trial—no credit card
                required.
              </p>
              <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/get-started"
                  className="inline-flex items-center justify-center rounded-xl bg-white text-neutral-900 px-8 py-4 text-lg font-semibold shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105"
                >
                  Start Your Free Trial
                  <span className="ml-2">→</span>
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-xl border border-white/20 text-white px-8 py-4 text-lg font-semibold transition-all duration-300 hover:bg-white/10 hover:scale-105"
                >
                  Schedule Demo
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer - Modern Design */}
      <footer className="bg-white border-t border-neutral-200">
        <div className="container-lg">
          <div className="py-16">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center">
                    <span className="text-white font-bold text-lg">L</span>
                  </div>
                  <span className="text-2xl font-bold text-neutral-900">
                    LibraryCloud
                  </span>
                </div>
                <p className="text-body text-neutral-600">
                  Transforming libraries worldwide with modern, cloud-based
                  management solutions.
                </p>
                <div className="mt-6 flex space-x-4">
                  <a
                    href="#"
                    className="text-neutral-400 hover:text-primary-600 transition-colors"
                  >
                    <span className="sr-only">Twitter</span>
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="text-neutral-400 hover:text-primary-600 transition-colors"
                  >
                    <span className="sr-only">LinkedIn</span>
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 19.418 1.595 20 2.328 20h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                  <a
                    href="#"
                    className="text-neutral-400 hover:text-primary-600 transition-colors"
                  >
                    <span className="sr-only">GitHub</span>
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </a>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-neutral-900 mb-4">
                  Product
                </h3>
                <ul className="space-y-3">
                  <li>
                    <Link
                      href="/features"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Features
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/pricing"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Pricing
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/demo"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Demo
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-neutral-900 mb-4">
                  Support
                </h3>
                <ul className="space-y-3">
                  <li>
                    <Link
                      href="/help"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Help Center
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/contact"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Contact
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/training"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Training
                    </Link>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-neutral-900 mb-4">
                  Company
                </h3>
                <ul className="space-y-3">
                  <li>
                    <Link
                      href="/about"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      About
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/privacy"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Privacy
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/terms"
                      className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                    >
                      Terms
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
            <div className="mt-12 pt-8 border-t border-neutral-200">
              <div className="flex flex-col sm:flex-row justify-between items-center">
                <div className="text-body text-neutral-600">
                  © 2024 LibraryCloud. All rights reserved.
                </div>
                <div className="flex space-x-6 mt-4 sm:mt-0">
                  <Link
                    href="/privacy"
                    className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                  >
                    Privacy
                  </Link>
                  <Link
                    href="/terms"
                    className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                  >
                    Terms
                  </Link>
                  <Link
                    href="/support"
                    className="text-body text-neutral-600 hover:text-primary-600 transition-colors"
                  >
                    Support
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
